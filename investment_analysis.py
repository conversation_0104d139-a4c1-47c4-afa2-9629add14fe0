import yfinance as yf
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

class InvestmentAnalyzer:
    def __init__(self, initial_capital: float = 10000):
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.trades = []
        self.daily_returns = []
        
    def calculate_position_size(self, price: float, lot_size: float = 0.1) -> float:
        """计算基于资金的仓位大小"""
        # 使用固定比例的资金进行每次交易
        risk_per_trade = 0.02  # 每次交易风险2%的资金
        position_value = self.current_capital * risk_per_trade
        shares = position_value / price
        return shares
    
    def simulate_trade(self, entry_price: float, direction: str, 
                      stop_loss: float, take_profit: float, 
                      lot_size: float = 0.1) -> Dict:
        """模拟单次交易"""
        shares = self.calculate_position_size(entry_price, lot_size)
        
        # 计算风险和收益
        if direction == 'long':
            risk_per_share = entry_price - stop_loss
            reward_per_share = take_profit - entry_price
        else:  # short
            risk_per_share = stop_loss - entry_price
            reward_per_share = entry_price - take_profit
        
        max_loss = shares * risk_per_share
        max_profit = shares * reward_per_share
        
        return {
            'direction': direction,
            'entry_price': entry_price,
            'shares': shares,
            'stop_loss': stop_loss,
            'take_profit': take_profit,
            'max_loss': max_loss,
            'max_profit': max_profit,
            'risk_reward_ratio': max_profit / max_loss if max_loss > 0 else 0
        }
    
    def backtest_strategy(self, symbol: str = 'AAPL', 
                         days: int = 252,  # 一年交易日
                         win_rate: float = 0.67) -> Dict:
        """回测策略"""
        print(f"开始回测 {symbol} 策略...")
        print(f"初始资金: ${self.initial_capital:,.2f}")
        
        # 获取历史数据
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days + 100)  # 多获取一些数据用于计算历史最大最小值
        
        try:
            data = yf.download(symbol, start=start_date, end=end_date, progress=False)
            if len(data) < 100:
                print("数据不足，无法进行回测")
                return {}
        except Exception as e:
            print(f"获取数据失败: {e}")
            return {}
        
        # 计算历史最大最小值（使用前100天数据）
        history_data = data.iloc[:100]
        history_max = history_data['High'].max()
        history_min = history_data['Low'].min()
        
        # 设置网格参数
        grid_levels = 100
        grid_step = (history_max - history_min) / grid_levels
        
        print(f"历史最高价: ${float(history_max):.2f}")
        print(f"历史最低价: ${float(history_min):.2f}")
        print(f"网格步长: ${float(grid_step):.2f}")
        
        # 模拟交易
        backtest_data = data.iloc[100:]  # 使用后面的数据进行回测
        total_trades = 0
        winning_trades = 0
        
        for i in range(len(backtest_data)):
            current_price = backtest_data['Close'].iloc[i]
            
            # 检查价格是否在网格范围内
            if current_price <= history_min or current_price >= history_max:
                continue
            
            # 计算网格位置
            grid_pos = int((current_price - history_min) / grid_step)
            grid_center = history_min + (grid_pos * grid_step) + (grid_step / 2)
            
            # 计算交易参数
            stop_distance = grid_step * 0.5
            profit_distance = stop_distance * 2.0  # 风险收益比2:1
            
            if current_price < grid_center:
                # 做多
                direction = 'long'
                stop_loss = current_price - stop_distance
                take_profit = current_price + profit_distance
            else:
                # 做空
                direction = 'short'
                stop_loss = current_price + stop_distance
                take_profit = current_price - profit_distance
            
            # 模拟交易
            trade = self.simulate_trade(current_price, direction, stop_loss, take_profit)
            
            # 模拟交易结果（基于胜率）
            is_winner = np.random.random() < win_rate
            
            if is_winner:
                profit = trade['max_profit']
                winning_trades += 1
            else:
                profit = -trade['max_loss']
            
            self.current_capital += profit
            total_trades += 1
            
            # 记录交易
            self.trades.append({
                'date': backtest_data.index[i],
                'price': current_price,
                'direction': direction,
                'profit': profit,
                'capital': self.current_capital,
                'is_winner': is_winner
            })
            
            # 限制交易频率（每5天最多一次交易）
            if total_trades % 5 == 0:
                continue
        
        # 计算统计数据
        total_return = self.current_capital - self.initial_capital
        return_percentage = (total_return / self.initial_capital) * 100
        actual_win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        # 计算最大回撤
        capital_series = [trade['capital'] for trade in self.trades]
        peak = self.initial_capital
        max_drawdown = 0
        
        for capital in capital_series:
            if capital > peak:
                peak = capital
            drawdown = (peak - capital) / peak
            if drawdown > max_drawdown:
                max_drawdown = drawdown
        
        results = {
            'initial_capital': self.initial_capital,
            'final_capital': self.current_capital,
            'total_return': total_return,
            'return_percentage': return_percentage,
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'actual_win_rate': actual_win_rate,
            'max_drawdown': max_drawdown * 100,
            'trades_per_month': total_trades / (days / 30),
            'avg_profit_per_trade': total_return / total_trades if total_trades > 0 else 0
        }
        
        return results
    
    def print_detailed_results(self, results: Dict):
        """打印详细结果"""
        print("\n" + "="*60)
        print("详细投资回报分析")
        print("="*60)
        
        print(f"初始投资: ${results['initial_capital']:,.2f}")
        print(f"最终资金: ${results['final_capital']:,.2f}")
        print(f"总收益: ${results['total_return']:,.2f}")
        print(f"收益率: {results['return_percentage']:.2f}%")
        print(f"年化收益率: {results['return_percentage']:.2f}%")  # 假设回测一年
        
        print(f"\n交易统计:")
        print(f"总交易次数: {results['total_trades']}")
        print(f"获利交易: {results['winning_trades']}")
        print(f"实际胜率: {results['actual_win_rate']:.2%}")
        print(f"平均每月交易: {results['trades_per_month']:.1f}次")
        print(f"平均每笔收益: ${results['avg_profit_per_trade']:.2f}")
        
        print(f"\n风险指标:")
        print(f"最大回撤: {results['max_drawdown']:.2f}%")
        
        # 计算夏普比率（简化版）
        if len(self.trades) > 0:
            returns = [trade['profit'] / self.initial_capital for trade in self.trades]
            avg_return = np.mean(returns)
            std_return = np.std(returns)
            sharpe_ratio = avg_return / std_return if std_return > 0 else 0
            print(f"夏普比率: {sharpe_ratio:.2f}")
    
    def plot_equity_curve(self):
        """绘制资金曲线"""
        if not self.trades:
            print("没有交易数据可绘制")
            return
        
        dates = [trade['date'] for trade in self.trades]
        capitals = [trade['capital'] for trade in self.trades]
        
        plt.figure(figsize=(12, 6))
        plt.plot(dates, capitals, linewidth=2, label='资金曲线')
        plt.axhline(y=self.initial_capital, color='r', linestyle='--', label='初始资金')
        plt.title('投资组合资金曲线')
        plt.xlabel('日期')
        plt.ylabel('资金 ($)')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.show()

def main():
    """主函数 - 计算10000投资的详细回报"""
    print("网格交易策略投资回报计算")
    print("="*50)
    
    # 创建分析器
    analyzer = InvestmentAnalyzer(initial_capital=10000)
    
    # 回测不同股票
    symbols = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'NVDA']
    
    for symbol in symbols:
        print(f"\n分析 {symbol}...")
        analyzer_temp = InvestmentAnalyzer(initial_capital=10000)
        results = analyzer_temp.backtest_strategy(symbol=symbol, days=252, win_rate=0.67)
        
        if results:
            print(f"\n{symbol} 投资结果:")
            print(f"收益: ${results['total_return']:,.2f} ({results['return_percentage']:.2f}%)")
            print(f"交易次数: {results['total_trades']}")
            print(f"胜率: {results['actual_win_rate']:.2%}")
            print(f"最大回撤: {results['max_drawdown']:.2f}%")
    
    # 详细分析AAPL
    print(f"\n{'='*60}")
    print("AAPL 详细分析 (主要标的)")
    print(f"{'='*60}")
    
    analyzer = InvestmentAnalyzer(initial_capital=10000)
    results = analyzer.backtest_strategy(symbol='AAPL', days=252, win_rate=0.67)
    
    if results:
        analyzer.print_detailed_results(results)
        
        # 绘制资金曲线
        try:
            analyzer.plot_equity_curve()
        except:
            print("无法显示图表")

if __name__ == "__main__":
    main()
