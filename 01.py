import yfinance as yf
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import time
from typing import Tuple, List, Optional

class GridTraderMaxMin:
    def __init__(self, 
                 symbol: str = 'AAPL',
                 history_bars: int = 1000,
                 grid_levels: int = 100,
                 lot_size: float = 0.1,
                 risk_reward_ratio: float = 2.0,
                 win_rate_target: float = 0.67,
                 auto_update_grid: bool = True,
                 update_interval: int = 100,
                 use_time_filter: bool = False,
                 start_hour: int = 9,
                 end_hour: int = 17):
        
        self.symbol = symbol
        self.history_bars = history_bars
        self.grid_levels = grid_levels
        self.lot_size = lot_size
        self.risk_reward_ratio = risk_reward_ratio
        self.win_rate_target = win_rate_target
        self.auto_update_grid = auto_update_grid
        self.update_interval = update_interval
        self.use_time_filter = use_time_filter
        self.start_hour = start_hour
        self.end_hour = end_hour
        
        # State variables
        self.history_max: float = 0.0
        self.history_min: float = 0.0
        self.grid_step: float = 0.0
        self.grid_levels_array: np.ndarray = np.array([])
        self.grid_initialized: bool = False
        self.tick_counter: int = 0
        self.total_trades: int = 0
        self.win_trades: int = 0
        self.current_position: Optional[str] = None  # 'long' or 'short' or None
        
        # Initialize grid
        if not self.initialize_grid():
            raise Exception("Grid initialization failed")
    
    def initialize_grid(self) -> bool:
        """Initialize the grid system with historical data"""
        if not self.get_history_max_min():
            print("Failed to get historical max/min")
            return False
        
        # Calculate grid step
        self.grid_step = (self.history_max - self.history_min) / self.grid_levels
        
        # Initialize grid levels array
        self.grid_levels_array = np.array([
            self.history_min + (i * self.grid_step) 
            for i in range(self.grid_levels + 1)
        ])
        
        self.grid_initialized = True
        print(f"Grid initialized: Max={self.history_max:.2f}, Min={self.history_min:.2f}, Step={self.grid_step:.2f}")
        return True
    
    def get_history_max_min(self) -> bool:
        """Get historical max and min prices from Yahoo Finance"""
        try:
            # Get historical data - using daily data for simplicity
            end_date = datetime.now()
            start_date = end_date - timedelta(days=self.history_bars)
            
            data = yf.download(self.symbol, start=start_date, end=end_date)
            
            if len(data) < self.history_bars:
                print(f"Warning: Only got {len(data)} bars, requested {self.history_bars}")
            
            self.history_max = data['High'].max()
            self.history_min = data['Low'].min()
            
            if self.history_max <= self.history_min:
                print(f"Invalid historical data: Max={self.history_max}, Min={self.history_min}")
                return False
                
            return True
            
        except Exception as e:
            print(f"Error getting historical data: {e}")
            return False
    
    def is_trade_time(self) -> bool:
        """Check if current time is within trading hours"""
        if not self.use_time_filter:
            return True
            
        now = datetime.now()
        return self.start_hour <= now.hour < self.end_hour
    
    def update_trade_stats(self, trade_result: bool) -> None:
        """Update trade statistics"""
        self.total_trades += 1
        if trade_result:
            self.win_trades += 1
        
        current_win_rate = self.win_trades / self.total_trades if self.total_trades > 0 else 0
        print(f"Trade stats: Total={self.total_trades}, Wins={self.win_trades}, Win Rate={current_win_rate:.2%}")
    
    def has_position(self) -> bool:
        """Check if we currently have a position"""
        return self.current_position is not None
    
    def get_grid_position(self, price: float) -> int:
        """Get the current price's position in the grid"""
        if price <= self.history_min:
            return 0
        if price >= self.history_max:
            return self.grid_levels
        
        return int((price - self.history_min) / self.grid_step)
    
    def calculate_trade_params(self, current_price: float, grid_pos: int) -> Tuple[str, float, float]:
        """
        Calculate trade direction, stop loss and take profit
        Returns: (direction, stop_loss, take_profit)
        """
        grid_center = self.grid_levels_array[grid_pos] + (self.grid_step / 2)
        stop_distance = self.grid_step * 0.5  # Stop loss distance is half grid step
        profit_distance = stop_distance * self.risk_reward_ratio
        
        if current_price < grid_center:
            # Price in lower half of grid - go long
            direction = 'long'
            stop_loss = current_price - stop_distance
            take_profit = current_price + profit_distance
        else:
            # Price in upper half of grid - go short
            direction = 'short'
            stop_loss = current_price + stop_distance
            take_profit = current_price - profit_distance
        
        return direction, stop_loss, take_profit
    
    def execute_trade(self, direction: str, stop_loss: float, take_profit: float) -> bool:
        """Simulate executing a trade (in real trading, connect to your broker API here)"""
        current_price = self.get_current_price()
        
        print(f"Executing {direction} trade at {current_price:.2f}")
        print(f"Stop Loss: {stop_loss:.2f}, Take Profit: {take_profit:.2f}")
        
        # In a real implementation, you would call your broker's API here
        # For simulation, we'll just track the position
        self.current_position = direction
        
        # Simulate trade result after some time
        # In a real implementation, you would monitor the position
        return True  # Simulate successful trade
    
    def get_current_price(self) -> float:
        """Get current price from Yahoo Finance"""
        try:
            data = yf.download(self.symbol, period='1d', interval='1m')
            return data['Close'].iloc[-1]
        except Exception as e:
            print(f"Error getting current price: {e}")
            return 0.0
    
    def check_position_result(self) -> Optional[bool]:
        """
        Check if current position has hit SL or TP
        Returns: True if profit, False if loss, None if still open
        """
        if not self.current_position:
            return None
            
        current_price = self.get_current_price()
        
        if self.current_position == 'long':
            # Check if price hit TP or SL
            if current_price >= self.current_take_profit:
                return True
            elif current_price <= self.current_stop_loss:
                return False
        else:  # short position
            if current_price <= self.current_take_profit:
                return True
            elif current_price >= self.current_stop_loss:
                return False
                
        return None
    
    def run(self):
        """Main trading loop"""
        print("Starting grid trading strategy...")
        
        while True:
            try:
                self.tick_counter += 1
                
                if not self.grid_initialized:
                    time.sleep(1)
                    continue
                
                if not self.is_trade_time():
                    time.sleep(60)  # Check less frequently when outside trading hours
                    continue
                
                # Check existing position
                if self.has_position():
                    result = self.check_position_result()
                    if result is not None:
                        self.update_trade_stats(result)
                        self.current_position = None
                    else:
                        time.sleep(1)
                        continue
                
                # Auto-update grid
                if self.auto_update_grid and self.tick_counter % self.update_interval == 0:
                    print("Auto-updating grid...")
                    self.initialize_grid()
                
                # Get current price
                current_price = self.get_current_price()
                if current_price == 0:
                    time.sleep(1)
                    continue
                
                # Check if price is within grid range
                if current_price <= self.history_min or current_price >= self.history_max:
                    print("Price outside historical range, reinitializing grid...")
                    self.initialize_grid()
                    time.sleep(1)
                    continue
                
                # Get grid position
                grid_pos = self.get_grid_position(current_price)
                
                # Adjust trading frequency based on win rate
                current_win_rate = (self.win_trades / self.total_trades if self.total_trades > 0 
                                  else self.win_rate_target)
                if self.total_trades > 10 and current_win_rate < self.win_rate_target * 0.8:
                    if self.tick_counter % 5 != 0:  # Reduce trading frequency
                        time.sleep(1)
                        continue
                
                # Calculate trade parameters
                direction, stop_loss, take_profit = self.calculate_trade_params(
                    current_price, grid_pos)
                
                # Save SL/TP for position monitoring
                self.current_stop_loss = stop_loss
                self.current_take_profit = take_profit
                
                # Execute trade
                if self.execute_trade(direction, stop_loss, take_profit):
                    print(f"Trade executed: {direction} at {current_price:.2f}")
                
                time.sleep(1)  # Wait before next tick
                
            except KeyboardInterrupt:
                print("Stopping strategy...")
                break
            except Exception as e:
                print(f"Error in main loop: {e}")
                time.sleep(5)

if __name__ == "__main__":
    # Example usage
    trader = GridTraderMaxMin(
        symbol='AAPL',
        history_bars=1000,
        grid_levels=100,
        lot_size=0.1,
        risk_reward_ratio=2.0,
        win_rate_target=0.67,
        auto_update_grid=True,
        update_interval=100,
        use_time_filter=False
    )
    
    trader.run()