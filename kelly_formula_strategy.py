import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class KellyFormulaTrader:
    def __init__(self, initial_capital: float = 12500):
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.trades = []
        self.win_rate = 0.67  # 67%胜率
        self.odds_ratio = 2.0  # 1:2赔率
        
    def calculate_kelly_fraction(self, win_rate: float, odds_ratio: float) -> float:
        """
        凯尔公式: f = (bp - q) / b
        其中:
        f = 应投注的资金比例
        b = 赔率 (这里是2)
        p = 胜率 (0.67)
        q = 败率 (1-p = 0.33)
        """
        b = odds_ratio
        p = win_rate
        q = 1 - p
        
        kelly_fraction = (b * p - q) / b
        return max(0, kelly_fraction)  # 确保不为负数
    
    def calculate_position_size(self, kelly_fraction: float, safety_factor: float = 0.25) -> float:
        """
        计算实际仓位大小
        使用安全系数降低风险 (通常使用1/4凯尔公式)
        """
        return kelly_fraction * safety_factor
    
    def simulate_grid_trading(self, days: int = 252, strategy_type: str = "conservative"):
        """模拟网格交易"""
        
        # 计算凯尔公式
        kelly_fraction = self.calculate_kelly_fraction(self.win_rate, self.odds_ratio)
        
        # 不同策略的安全系数
        safety_factors = {
            "conservative": 0.25,  # 1/4凯尔
            "moderate": 0.5,       # 1/2凯尔
            "aggressive": 0.75     # 3/4凯尔
        }
        
        safety_factor = safety_factors.get(strategy_type, 0.25)
        position_fraction = self.calculate_position_size(kelly_fraction, safety_factor)
        
        print(f"凯尔公式计算结果:")
        print(f"理论最优仓位: {kelly_fraction:.3f} ({kelly_fraction*100:.1f}%)")
        print(f"实际使用仓位: {position_fraction:.3f} ({position_fraction*100:.1f}%)")
        print(f"策略类型: {strategy_type}")
        print()
        
        # 模拟交易
        total_trades = 0
        winning_trades = 0
        
        # 根据策略类型调整交易频率
        trade_frequencies = {
            "conservative": 0.3,   # 30%的天数交易
            "moderate": 0.5,       # 50%的天数交易  
            "aggressive": 0.8      # 80%的天数交易
        }
        
        trade_frequency = trade_frequencies.get(strategy_type, 0.3)
        
        for day in range(days):
            # 决定是否交易
            if np.random.random() > trade_frequency:
                continue
                
            # 计算当前交易金额
            trade_amount = self.current_capital * position_fraction
            
            # 模拟交易结果
            is_winner = np.random.random() < self.win_rate
            
            if is_winner:
                profit = trade_amount * self.odds_ratio  # 赢得2倍
                winning_trades += 1
            else:
                profit = -trade_amount  # 损失本金
            
            self.current_capital += profit
            total_trades += 1
            
            # 记录交易
            self.trades.append({
                'day': day,
                'trade_amount': trade_amount,
                'profit': profit,
                'capital': self.current_capital,
                'is_winner': is_winner
            })
            
            # 防止资金耗尽
            if self.current_capital <= self.initial_capital * 0.1:
                print(f"资金严重亏损，在第{day}天停止交易")
                break
        
        # 计算统计数据
        actual_win_rate = winning_trades / total_trades if total_trades > 0 else 0
        total_return = self.current_capital - self.initial_capital
        return_percentage = (total_return / self.initial_capital) * 100
        
        return {
            'strategy_type': strategy_type,
            'kelly_fraction': kelly_fraction,
            'position_fraction': position_fraction,
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'actual_win_rate': actual_win_rate,
            'final_capital': self.current_capital,
            'total_return': total_return,
            'return_percentage': return_percentage
        }

def analyze_different_strategies():
    """分析不同策略的表现"""
    
    print("="*80)
    print("基于凯尔公式的12,500港币投资策略分析")
    print("="*80)
    print(f"初始资金: HK$12,500")
    print(f"胜率: 67%")
    print(f"赔率: 1:2")
    print()
    
    strategies = ["conservative", "moderate", "aggressive"]
    results = []
    
    for strategy in strategies:
        print(f"{'='*50}")
        print(f"分析 {strategy.upper()} 策略")
        print(f"{'='*50}")
        
        # 运行多次模拟取平均值
        simulations = []
        for _ in range(100):  # 100次模拟
            trader = KellyFormulaTrader(initial_capital=12500)
            result = trader.simulate_grid_trading(days=252, strategy_type=strategy)
            simulations.append(result)
        
        # 计算平均结果
        avg_result = {
            'strategy_type': strategy,
            'kelly_fraction': simulations[0]['kelly_fraction'],
            'position_fraction': simulations[0]['position_fraction'],
            'avg_trades': np.mean([s['total_trades'] for s in simulations]),
            'avg_win_rate': np.mean([s['actual_win_rate'] for s in simulations]),
            'avg_final_capital': np.mean([s['final_capital'] for s in simulations]),
            'avg_return': np.mean([s['total_return'] for s in simulations]),
            'avg_return_pct': np.mean([s['return_percentage'] for s in simulations]),
            'success_rate': len([s for s in simulations if s['final_capital'] > 12500]) / len(simulations)
        }
        
        results.append(avg_result)
        
        print(f"凯尔最优仓位: {avg_result['kelly_fraction']:.3f} ({avg_result['kelly_fraction']*100:.1f}%)")
        print(f"实际使用仓位: {avg_result['position_fraction']:.3f} ({avg_result['position_fraction']*100:.1f}%)")
        print(f"平均交易次数: {avg_result['avg_trades']:.0f}")
        print(f"平均胜率: {avg_result['avg_win_rate']:.2%}")
        print(f"平均最终资金: HK${avg_result['avg_final_capital']:,.0f}")
        print(f"平均收益: HK${avg_result['avg_return']:,.0f}")
        print(f"平均收益率: {avg_result['avg_return_pct']:.2f}%")
        print(f"盈利概率: {avg_result['success_rate']:.1%}")
        print()
    
    # 比较分析
    print("="*80)
    print("策略比较分析")
    print("="*80)
    
    print(f"{'策略':<12} {'仓位比例':<8} {'平均收益':<10} {'收益率':<8} {'盈利概率':<8} {'推荐度':<8}")
    print("-" * 70)
    
    for result in results:
        recommendation = "★★★" if result['avg_return_pct'] > 5 and result['success_rate'] > 0.7 else \
                        "★★☆" if result['avg_return_pct'] > 2 and result['success_rate'] > 0.6 else "★☆☆"
        
        print(f"{result['strategy_type']:<12} {result['position_fraction']*100:<6.1f}% "
              f"HK${result['avg_return']:<8,.0f} {result['avg_return_pct']:<6.2f}% "
              f"{result['success_rate']:<7.1%} {recommendation:<8}")
    
    return results

def calculate_optimal_parameters():
    """计算最优参数"""
    
    print("\n" + "="*80)
    print("凯尔公式最优参数计算")
    print("="*80)
    
    # 测试不同胜率和赔率组合
    win_rates = [0.60, 0.65, 0.67, 0.70, 0.75]
    odds_ratios = [1.5, 2.0, 2.5, 3.0]
    
    print("不同胜率和赔率组合的凯尔公式结果:")
    print(f"{'胜率':<6} {'赔率':<6} {'凯尔公式':<10} {'建议仓位':<10} {'期望收益':<10}")
    print("-" * 50)
    
    best_combination = None
    best_expected_return = 0
    
    for win_rate in win_rates:
        for odds_ratio in odds_ratios:
            # 计算凯尔公式
            b = odds_ratio
            p = win_rate
            q = 1 - p
            kelly_fraction = (b * p - q) / b
            
            if kelly_fraction > 0:
                # 使用1/4凯尔作为实际仓位
                suggested_position = kelly_fraction * 0.25
                
                # 计算期望收益率
                expected_return = p * odds_ratio - q
                
                print(f"{p:<6.0%} {odds_ratio:<6.1f} {kelly_fraction:<10.3f} "
                      f"{suggested_position:<10.3f} {expected_return:<10.3f}")
                
                if expected_return > best_expected_return:
                    best_expected_return = expected_return
                    best_combination = {
                        'win_rate': win_rate,
                        'odds_ratio': odds_ratio,
                        'kelly_fraction': kelly_fraction,
                        'suggested_position': suggested_position,
                        'expected_return': expected_return
                    }
    
    print(f"\n最优组合:")
    if best_combination:
        print(f"胜率: {best_combination['win_rate']:.0%}")
        print(f"赔率: 1:{best_combination['odds_ratio']:.1f}")
        print(f"凯尔公式: {best_combination['kelly_fraction']:.3f}")
        print(f"建议仓位: {best_combination['suggested_position']:.3f} ({best_combination['suggested_position']*100:.1f}%)")
        print(f"期望收益: {best_combination['expected_return']:.3f}")

def practical_implementation_guide():
    """实际实施指南"""
    
    print("\n" + "="*80)
    print("12,500港币实际实施指南")
    print("="*80)
    
    # 基于67%胜率，1:2赔率的凯尔公式
    win_rate = 0.67
    odds_ratio = 2.0
    kelly_fraction = (odds_ratio * win_rate - (1 - win_rate)) / odds_ratio
    conservative_position = kelly_fraction * 0.25
    
    capital = 12500
    
    print(f"基础参数:")
    print(f"• 初始资金: HK${capital:,}")
    print(f"• 目标胜率: {win_rate:.0%}")
    print(f"• 目标赔率: 1:{odds_ratio}")
    print(f"• 凯尔最优仓位: {kelly_fraction:.3f} ({kelly_fraction*100:.1f}%)")
    print(f"• 保守实际仓位: {conservative_position:.3f} ({conservative_position*100:.1f}%)")
    print()
    
    print(f"具体操作方案:")
    print(f"1. 每次交易金额: HK${capital * conservative_position:,.0f}")
    print(f"2. 止损金额: HK${capital * conservative_position:,.0f}")
    print(f"3. 止盈目标: HK${capital * conservative_position * odds_ratio:,.0f}")
    print(f"4. 风险收益比: 1:{odds_ratio}")
    print()
    
    print(f"网格/马丁策略设置:")
    print(f"• 网格级数: 20-30级")
    print(f"• 每格资金: HK${capital * conservative_position / 20:,.0f}")
    print(f"• 马丁倍数: 1.2-1.5倍 (保守)")
    print(f"• 最大连续亏损: 3-4次")
    print()
    
    print(f"月度收益预期:")
    monthly_trades = 20  # 假设每月20次交易
    monthly_profit = monthly_trades * (win_rate * capital * conservative_position * odds_ratio - 
                                     (1-win_rate) * capital * conservative_position)
    print(f"• 预期月交易次数: {monthly_trades}")
    print(f"• 预期月收益: HK${monthly_profit:,.0f}")
    print(f"• 预期年收益: HK${monthly_profit * 12:,.0f}")
    print(f"• 年化收益率: {monthly_profit * 12 / capital * 100:.2f}%")

if __name__ == "__main__":
    # 运行完整分析
    results = analyze_different_strategies()
    calculate_optimal_parameters()
    practical_implementation_guide()
