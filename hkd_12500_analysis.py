import yfinance as yf
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def analyze_12500_hkd_investment():
    """分析12,500港币的HSI50投资回报"""
    
    print("="*80)
    print("12,500 HKD HSI50 网格交易策略投资分析")
    print("="*80)
    print(f"分析日期: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 汇率转换 (假设 1 USD = 7.8 HKD)
    usd_hkd_rate = 7.8
    hkd_capital = 12500
    usd_capital = hkd_capital / usd_hkd_rate
    
    print(f"初始资金: HK${hkd_capital:,}")
    print(f"等值美元: US${usd_capital:,.2f} (按汇率1:7.8计算)")
    print()
    
    # 港股相关标的分析
    hk_symbols = {
        '^HSI': '恒生指数',
        '2800.HK': '盈富基金 (恒生指数ETF)',
        '2828.HK': '恒生H股ETF',
        '0388.HK': '香港交易所',
        '0700.HK': '腾讯控股',
        '0941.HK': '中国移动',
        '1299.HK': '友邦保险'
    }
    
    print("1. 港股标的适合性分析")
    print("-" * 60)
    
    suitable_targets = []
    
    for symbol, name in hk_symbols.items():
        try:
            # 获取当前价格
            data = yf.download(symbol, period='5d', progress=False)
            if len(data) == 0:
                continue
                
            current_price = float(data['Close'].iloc[-1])
            
            # 计算最小投资单位
            if symbol.endswith('.HK'):
                min_lot = 100 if symbol in ['2800.HK', '2828.HK'] else 100  # ETF通常100股起
                min_investment_hkd = current_price * min_lot
            else:
                min_investment_hkd = current_price  # 指数类
                min_lot = 1
            
            # 判断是否适合当前资金
            affordable = min_investment_hkd <= hkd_capital * 0.1  # 单次投资不超过总资金10%
            
            suitable_targets.append({
                'symbol': symbol,
                'name': name,
                'price': current_price,
                'min_lot': min_lot,
                'min_investment': min_investment_hkd,
                'affordable': affordable
            })
            
            status = "✓ 适合" if affordable else "✗ 资金不足"
            print(f"{symbol:<12} {name:<20} HK${current_price:>8.2f} {min_lot:>6}股 HK${min_investment_hkd:>8,.0f} {status}")
            
        except Exception as e:
            print(f"{symbol:<12} {name:<20} 数据获取失败")
    
    print("\n2. 基于12,500港币的投资策略建议")
    print("-" * 60)
    
    # 筛选适合的标的
    affordable_targets = [t for t in suitable_targets if t['affordable']]
    
    if affordable_targets:
        print("推荐投资标的:")
        for target in affordable_targets[:3]:  # 显示前3个
            print(f"• {target['symbol']} - {target['name']}")
            print(f"  当前价格: HK${target['price']:.2f}")
            print(f"  最小投资: HK${target['min_investment']:,.0f}")
            
            # 计算可购买数量
            max_affordable_lots = int(hkd_capital * 0.8 / target['min_investment'])  # 使用80%资金
            total_investment = max_affordable_lots * target['min_investment']
            
            print(f"  建议购买: {max_affordable_lots}手 (HK${total_investment:,.0f})")
            print()
    
    print("3. 网格交易策略参数调整")
    print("-" * 60)
    
    # 基于较小资金的策略调整
    strategies = [
        {
            'name': '小资金保守型',
            'grid_levels': 20,
            'risk_reward': 1.5,
            'position_size': 0.05,  # 每次使用5%资金
            'description': '适合新手，风险较低'
        },
        {
            'name': '小资金平衡型', 
            'grid_levels': 50,
            'risk_reward': 2.0,
            'position_size': 0.08,  # 每次使用8%资金
            'description': '平衡风险与收益'
        },
        {
            'name': '小资金激进型',
            'grid_levels': 100,
            'risk_reward': 3.0,
            'position_size': 0.12,  # 每次使用12%资金
            'description': '追求更高收益，风险较大'
        }
    ]
    
    print(f"{'策略类型':<15} {'网格数':<6} {'风险收益比':<8} {'仓位比例':<8} {'说明':<15}")
    print("-" * 60)
    
    for strategy in strategies:
        print(f"{strategy['name']:<15} {strategy['grid_levels']:<6} "
              f"{strategy['risk_reward']:<8.1f} {strategy['position_size']*100:<6.0f}% "
              f"{strategy['description']:<15}")
    
    print("\n4. 12,500港币投资收益预测")
    print("-" * 60)
    
    # 基于之前分析调整收益预期（小资金可能有更高的灵活性）
    base_annual_return = 0.68  # 基础年收益率
    
    scenarios = [
        {
            'name': '保守预期',
            'monthly_return': base_annual_return / 12 * 0.8,  # 月收益率
            'probability': '80%',
            'description': '稳健操作，较少交易'
        },
        {
            'name': '基准预期',
            'monthly_return': base_annual_return / 12,
            'probability': '60%', 
            'description': '正常网格交易频率'
        },
        {
            'name': '乐观预期',
            'monthly_return': base_annual_return / 12 * 1.5,
            'probability': '40%',
            'description': '市场波动较大，交易机会多'
        }
    ]
    
    print(f"基于 HK${hkd_capital:,} 初始投资:")
    print()
    
    for scenario in scenarios:
        monthly_profit_hkd = hkd_capital * scenario['monthly_return'] / 100
        annual_profit_hkd = monthly_profit_hkd * 12
        final_capital_hkd = hkd_capital + annual_profit_hkd
        
        print(f"{scenario['name']} (概率: {scenario['probability']}):")
        print(f"  月收益率: {scenario['monthly_return']:.3f}%")
        print(f"  月收益金额: HK${monthly_profit_hkd:,.2f}")
        print(f"  年收益金额: HK${annual_profit_hkd:,.2f}")
        print(f"  一年后资金: HK${final_capital_hkd:,.2f}")
        print(f"  说明: {scenario['description']}")
        print()
    
    print("5. 实际操作建议")
    print("-" * 60)
    
    print("资金分配建议:")
    print(f"• 网格交易资金: HK${hkd_capital * 0.7:,.0f} (70%)")
    print(f"• 应急备用金: HK${hkd_capital * 0.2:,.0f} (20%)")
    print(f"• 学习试错金: HK${hkd_capital * 0.1:,.0f} (10%)")
    print()
    
    print("操作步骤:")
    print("1. 开设港股账户 (推荐富途、老虎证券等)")
    print("2. 选择2800.HK (盈富基金) 作为主要标的")
    print("3. 先用HK$1,250进行小额测试")
    print("4. 设置20级网格，每格约1%价差")
    print("5. 每次交易使用5-8%资金")
    print("6. 监控1个月后评估效果")
    print()
    
    print("风险控制:")
    print("• 单日最大亏损不超过HK$250 (2%)")
    print("• 总回撤超过HK$625 (5%) 时暂停策略")
    print("• 每周评估一次策略表现")
    print("• 保持学习心态，记录交易日志")
    print()
    
    print("6. 预期收益总结")
    print("-" * 60)
    
    # 最终总结
    conservative_annual = hkd_capital * (base_annual_return * 0.8) / 100
    realistic_annual = hkd_capital * base_annual_return / 100
    optimistic_annual = hkd_capital * (base_annual_return * 1.5) / 100
    
    print(f"基于HK${hkd_capital:,}投资，预期年收益:")
    print(f"• 保守估计: HK${conservative_annual:,.0f} ({conservative_annual/hkd_capital*100:.2f}%)")
    print(f"• 现实预期: HK${realistic_annual:,.0f} ({realistic_annual/hkd_capital*100:.2f}%)")
    print(f"• 乐观估计: HK${optimistic_annual:,.0f} ({optimistic_annual/hkd_capital*100:.2f}%)")
    print()
    
    print("重要提醒:")
    print("• 小资金投资更需要严格的风险控制")
    print("• 建议从最保守的策略开始")
    print("• 重点关注学习和经验积累")
    print("• 不要期望过高的收益率")
    print("• 考虑将此作为投资学习的起点")

if __name__ == "__main__":
    analyze_12500_hkd_investment()
