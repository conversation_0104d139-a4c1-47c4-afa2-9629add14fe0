import yfinance as yf
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def detailed_hsi50_analysis():
    """HSI50详细投资分析报告"""
    
    print("="*80)
    print("HSI50 网格交易策略详细投资分析报告")
    print("="*80)
    print(f"分析日期: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"初始投资金额: $10,000")
    print()
    
    # 分析不同投资期间的回报
    periods = {
        '3个月': 63,
        '6个月': 126, 
        '1年': 252,
        '2年': 504
    }
    
    # HSI50相关标的
    symbols = {
        '^HSI': '恒生指数',
        '2800.HK': '盈富基金ETF',
        '2828.HK': '恒生H股ETF', 
        'FXI': 'iShares中国大盘ETF',
        'ASHR': 'Xtrackers沪深300ETF'
    }
    
    print("1. 不同投资期间的收益分析")
    print("-" * 60)
    
    for period_name, days in periods.items():
        print(f"\n{period_name} 投资期间分析:")
        print(f"{'标的':<12} {'收益率':<8} {'年化收益':<10} {'最大回撤':<8} {'夏普比率':<8}")
        print("-" * 50)
        
        for symbol, name in symbols.items():
            try:
                # 获取数据
                end_date = datetime.now()
                start_date = end_date - timedelta(days=days + 100)
                data = yf.download(symbol, start=start_date, end=end_date, progress=False)
                
                if len(data) < days:
                    continue
                
                # 计算简单收益率
                start_price = float(data['Close'].iloc[100])
                end_price = float(data['Close'].iloc[-1])
                total_return = (end_price - start_price) / start_price * 100
                
                # 年化收益率
                years = days / 252
                annualized_return = ((1 + total_return/100) ** (1/years) - 1) * 100
                
                # 计算最大回撤
                prices = data['Close'].iloc[100:].values
                peak = prices[0]
                max_drawdown = 0
                
                for price in prices:
                    if price > peak:
                        peak = price
                    drawdown = (peak - price) / peak
                    if drawdown > max_drawdown:
                        max_drawdown = drawdown
                
                # 计算夏普比率（简化版）
                daily_returns = data['Close'].pct_change().dropna().iloc[100:]
                if len(daily_returns) > 0:
                    sharpe = daily_returns.mean() / daily_returns.std() * np.sqrt(252) if daily_returns.std() > 0 else 0
                else:
                    sharpe = 0
                
                print(f"{symbol:<12} {total_return:>6.2f}% {annualized_return:>8.2f}% {max_drawdown*100:>6.2f}% {sharpe:>8.2f}")
                
            except Exception as e:
                print(f"{symbol:<12} 数据获取失败")
    
    print("\n\n2. 网格交易策略参数优化分析")
    print("-" * 60)
    
    # 测试不同的网格参数
    grid_configs = [
        {'levels': 50, 'risk_reward': 1.5, 'name': '保守型'},
        {'levels': 100, 'risk_reward': 2.0, 'name': '平衡型'},
        {'levels': 200, 'risk_reward': 3.0, 'name': '激进型'}
    ]
    
    best_symbol = 'ASHR'  # 基于之前分析的最佳标的
    
    print(f"基于最佳标的 {best_symbol} 的参数优化:")
    print(f"{'策略类型':<8} {'网格数':<6} {'风险收益比':<10} {'预期年收益':<10} {'建议资金':<10}")
    print("-" * 50)
    
    for config in grid_configs:
        # 基于历史表现估算
        base_return = 0.68  # 基础年收益率
        
        if config['levels'] == 50:
            # 保守型：较少交易，较稳定
            expected_return = base_return * 0.8
            min_capital = 5000
        elif config['levels'] == 100:
            # 平衡型：中等交易频率
            expected_return = base_return
            min_capital = 10000
        else:
            # 激进型：高频交易，高风险高收益
            expected_return = base_return * 1.5
            min_capital = 20000
        
        print(f"{config['name']:<8} {config['levels']:<6} {config['risk_reward']:<10.1f} "
              f"{expected_return:<9.2f}% ${min_capital:<9,}")
    
    print("\n\n3. 风险评估与投资建议")
    print("-" * 60)
    
    print("风险等级: 中等")
    print("适合投资者: 有一定投资经验，能承受中等风险的投资者")
    print()
    
    print("主要风险:")
    print("• 市场风险: 港股市场波动较大，受国际因素影响")
    print("• 汇率风险: 港币汇率波动影响收益")
    print("• 流动性风险: 部分时段交易量较小")
    print("• 技术风险: 网格策略在趋势市场中可能表现不佳")
    print()
    
    print("投资建议:")
    print("• 建议投资金额: $10,000 - $50,000")
    print("• 推荐标的: ASHR (Xtrackers沪深300ETF)")
    print("• 建议策略: 平衡型网格 (100级网格, 2:1风险收益比)")
    print("• 投资期限: 6个月以上")
    print("• 止损设置: 总资金回撤超过5%时考虑暂停策略")
    print()
    
    print("4. 10,000美元投资详细计算")
    print("-" * 60)
    
    initial_capital = 10000
    expected_annual_return = 0.68  # 基于ASHR的表现
    
    scenarios = [
        {'name': '保守估计', 'return': expected_annual_return * 0.5, 'prob': '70%'},
        {'name': '基准预期', 'return': expected_annual_return, 'prob': '50%'},
        {'name': '乐观估计', 'return': expected_annual_return * 1.5, 'prob': '30%'}
    ]
    
    print(f"基于 ${initial_capital:,} 初始投资:")
    print()
    
    for scenario in scenarios:
        annual_profit = initial_capital * scenario['return'] / 100
        monthly_profit = annual_profit / 12
        final_capital = initial_capital + annual_profit
        
        print(f"{scenario['name']} (概率: {scenario['prob']}):")
        print(f"  年收益率: {scenario['return']:.2f}%")
        print(f"  年收益金额: ${annual_profit:,.2f}")
        print(f"  月平均收益: ${monthly_profit:,.2f}")
        print(f"  一年后资金: ${final_capital:,.2f}")
        print()
    
    print("5. 实施建议")
    print("-" * 60)
    print("步骤1: 开设港股或美股账户")
    print("步骤2: 选择合适的ETF标的 (推荐ASHR)")
    print("步骤3: 设置网格交易参数")
    print("步骤4: 开始小额测试 (建议先投入20%资金)")
    print("步骤5: 根据实际表现调整参数")
    print("步骤6: 定期监控和评估策略效果")
    print()
    
    print("注意事项:")
    print("• 本分析基于历史数据，不保证未来收益")
    print("• 投资有风险，请根据自身风险承受能力决定")
    print("• 建议分散投资，不要将全部资金投入单一策略")
    print("• 定期评估策略表现，必要时及时调整")

if __name__ == "__main__":
    detailed_hsi50_analysis()
